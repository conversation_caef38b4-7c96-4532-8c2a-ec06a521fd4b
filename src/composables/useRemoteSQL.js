import { computed, ref, watch, nextTick } from 'vue';
import { useWebSocket } from './useWebSocket';
import { useRestAPI } from './useRestAPI';
import databaseName from '../myFunctions/databaseName';
import auth2 from '../auth2'


export function useRemoteSQL(options = {}) {

	// Endpoint for WebSocket to use in this composable
	const wsEndpoint = options.wsEndpoint || databaseName.getWsUrl('monitorRemoteListen')

	// Create websocket options, including the URL
  const wsOptions = { ...options, url: wsEndpoint }

  // Message base for WebSocket
	const msgBase = databaseName.getWebsocketMessageBase();

  // Initialize the base WebSocket functionality
  const websocket = useWebSocket(wsOptions)

  const {
    isConnected,
    connectionStatus,
    messages,
    sendMessage,
    reconnect,
    disconnect,
    clearMessages,
		connect
  } = websocket

  // Initialize the REST API functionality
  const api = useRestAPI()

	// =========================================
  // Shared State
  // =========================================
  // Create a reactive store to hold data for all command types
	const lastAPIError = ref();
  const audit_log = ref([]);
	const error_log = ref([]);
	const trans_log_lpn = ref([]);
	const alarm_log = ref([]);
	const selectedSiteID = ref();
	const selectedDate = ref(new Date());
	const selectedMaxRecords = ref(30);
	const customerSites = ref('');
	const deviceMonitorData = ref([]);
	const availableCommands = ref([]);
	const commandResults = ref({});
	const commandVariables = ref({});
	// To stop watchers from reacting to the reset
	const isResetting = ref(false);
	const user = ref(null);

	// Initialize user data
	const initializeUser = async () => {
		const userResult = await auth2.getUser();
		if (userResult.isOk) {
			user.value = userResult.data;
		}
	};

	// Call initialization
	initializeUser();

	// =========================================
  // Loading & Error Handling
  // =========================================

  // Single loading state for all API operations
  const isLoading = ref(false)

  // Single error state for all API operations
  const error = ref(null)

  // Clear any existing error
  const clearError = () => {
    error.value = null
		lastAPIError.value = null;
  }

	// Function to reset all state
  const resetState = () => {
    isResetting.value = true;
    audit_log.value = [];
    error_log.value = null;
    trans_log_lpn.value = [];

    // Use nextTick to ensure Vue has processed the state changes
    nextTick(() => {
      isResetting.value = false;
    });

  };

	// Watch for connection status changes to trigger cleanup
	watch(connectionStatus, (newStatus, oldStatus) => {
		// If going from any connected state to DISCONNECTED
		if (newStatus === 'DISCONNECTED' &&
				(oldStatus === 'CONNECTED' || oldStatus === 'RECONNECTING')) {
			console.log('Connection lost, cleaning up state');
			resetState();
		}
	});

  // Helper to wrap API calls with loading and error handling
  const loadingAndErrorWrapper = async (operationName, callback) => {
    // Clear previous error
    clearError()
    // Set loading state
    isLoading.value = true
    try {
      // Execute the callback
      const result = await callback()
      return result
    }catch (err) {
      // Capture and store error with the operation name
      // error.value = { operation: operationName,message: err.message || `Error during ${operationName}`,timestamp: new Date() }

			lastAPIError.value = err.response?.data?.message || 'Unknown error occurred';
			return null
    } finally {
      // Clear loading state
      isLoading.value = false
    }
  }

	// Helper function to safely parse potentially double-encoded JSON
  const safeJsonParse = (jsonString) => {
    try {
      // console.log('jsonString:',jsonString)
      // First parse attempt
      const parsed = JSON.parse(jsonString);

      // If the result is a string and looks like JSON, parse it again
      if (typeof parsed === 'string' &&
        (parsed.startsWith('[') || parsed.startsWith('{'))) {
        try {
          return JSON.parse(parsed);
        } catch (innerError) {
          // console.warn('Second JSON parse failed, using first result', innerError);
          return parsed;
        }
      }

      // If it's not a string or doesn't look like JSON, return the first parse
      return parsed;
    } catch (error) {
      // console.error('JSON parsing failed:', error);
      return false // Re-throw to let caller handle it
    }
  };

	// =========================================
  // REST API functions
  // =========================================
	const fetchCustomerSites = async (params = {}) => {
		return loadingAndErrorWrapper('Fetch Customer Sites', async () => {

			let endpoint = '';
			if (user.value.user_type === 'internal') {
				endpoint = 'OverallScreen';
			}else {
				endpoint = `CustomerOverallScreen/${user.value?.idcustomers}`;
			}
			const data = await api.get(`/RemoteStats/${endpoint}`, params)
			customerSites.value = data
			// for demo but by default select the first site
			// selectedSiteID.value = data.data.find(site => site.idcustomers_sites === 10)?.idcustomers_sites
			return data
		})
	}
	const fetchConnectionsBySite = async (params = {}) => {
		return loadingAndErrorWrapper('Fetch Connections By Site', async () => {
			// Check if selectedSiteID is defined before making the API call
			if (!selectedSiteID.value) {
				console.log('No site selected yet, skipping fetchConnectionsBySite');
				return { data: [] }; // Return empty data instead of making API call
			}
			const data = await api.get(`/RemoteStats/OverallScreen/${selectedSiteID.value}`, params);

			deviceMonitorData.value = data
			return data;
		});
	}
	/**
   * Fetch Available SQL Commands from the Rest API
   *
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>}
   */
		const fetchAvailableSQLCommands = async (params = {}) => {
			return loadingAndErrorWrapper('Fetch Available SQL Commands', async () => {
				const data = await api.get(`/RemoteStats/RemoteSQL/${selectedSiteID.value}`, params)

				return data
			})
		}
  /**
   * Fetch Available SQL Commands from the Rest API
   *
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>}
   */
	const fetchAvailableCommands = async () => {
		return loadingAndErrorWrapper('Fetch Available Commands', async () => {
			const response = await fetchAvailableSQLCommands();
			if (response && response.data) {
				availableCommands.value = response.data || [];

				// Initialize command variables and results
				if (Array.isArray(availableCommands.value)) {
					availableCommands.value.forEach(cmd => {
						if (!cmd) return;

						// Initialize results storage
						if (!commandResults.value[cmd.remote_cmd_name]) {
							commandResults.value[cmd.remote_cmd_name] = [];
						}

						// Initialize variables for commands that need them
						if (hasCommandVariables(cmd)) {
							if (!commandVariables.value[cmd.remote_cmd_name]) {
								commandVariables.value[cmd.remote_cmd_name] = {};
							}

							if (Array.isArray(cmd.remote_cmd_vars)) {
								cmd.remote_cmd_vars.forEach(variable => {
									if (variable && variable.field_replace_name) {
										commandVariables.value[cmd.remote_cmd_name][variable.field_replace_name] = '';
									}
								});
							}
						}
					});
				}
			}

			return response;
		});
	};

	const hasCommandVariables = (command) => {
		return 	command &&
						command.remote_cmd_vars &&
						Array.isArray(command.remote_cmd_vars) &&
						command.remote_cmd_vars.length > 0;
	};

	const executeRemoteSQLCommand = async (command) => {
		return loadingAndErrorWrapper(`Execute Command ${command.remote_cmd_name}`, async () => {
			// Prepare variables if any
			const remoteVars = [];
			if (hasCommandVariables(command)) {
				command.remote_cmd_vars.forEach(variable => {
					remoteVars.push({
						field_replace_name: variable.field_replace_name,
						field_value: commandVariables.value[command.remote_cmd_name][variable.field_replace_name]
					});
				});
			}

			// Send command with variables
			return sendCommand(command.remote_cmd_name, {
				remote_cmd_name: command.remote_cmd_name,
				customer_site_id: selectedSiteID.value,
				limit: selectedMaxRecords.value,
				cmd_ref_val: command.remote_cmd_name,
				remote_vars: remoteVars.length > 0 ? remoteVars : null
			});
		});
	};


	// =========================================
  // WebSocket message commands
  // =========================================

	// Send ESTABLISH message
	const sendEstablishMessage = () => {
		const msgObj = {
			msgType: "ESTABLISH",
			msgFromService: 'REMOTE-SQL',
			msgFromUser: "",
			EstablishConnObj: {
				messageGroup: 'REMOTE-SQL',
				userIdName: user.value?.userIdName || user.value?.username,
				userSecurity: user.value?.userSecurity || user.value?.security_level,
				message: ""
			}
		}
		return sendMessage({...msgBase.value,...msgObj})
	}

	// Send REMOTE-SQL command
  const sendCommand = (cmd_ref_val, obj, ) => {

    const msgObj = {
      msgType: "COMMAND",
      msgFromService: 'REMOTE-SQL',
      msgFromUser: "",
      CommandObj: {
        cmdName: "REMOTE-SQL",
				// Override with function param just in case we screw up the command obj
        cmdMessage: JSON.stringify({...obj, cmd_ref_val})
      }
    }

		return sendMessage({...msgBase.value,...msgObj})
  }

	// =========================================
  // WEB-SOCKET: PROCESS MESSAGES
  // =========================================
  watch(() => messages, (newMessages) => {
		if (newMessages.length === 0) return;

		const msg = newMessages[newMessages.length - 1];

		// Handle different command types
		if (msg.CommandObj) {
			const cmdMessage = msg.CommandObj.cmdMessage;

			const jsonMsg = safeJsonParse(cmdMessage);

			// Update the appropriate result based on cmd_ref_val
			if (jsonMsg && jsonMsg.cmd_ref_val && Object.prototype.hasOwnProperty.call(commandResults.value, jsonMsg.cmd_ref_val)) {
				try {
					commandResults.value[jsonMsg.cmd_ref_val] = safeJsonParse(jsonMsg.data) || [];
				} catch (error) {
					console.error(`Error parsing JSON for ${jsonMsg.cmd_ref_val}:`, error);
				}
			}

		}
	}, { deep: true });



	return {
    // Re-export base WebSocket functionality
    ...websocket,

    //Send commands
    sendEstablishMessage,
    sendCommand,

		fetchAvailableCommands,
		hasCommandVariables,
		executeRemoteSQLCommand,

    // REST API methods
		fetchConnectionsBySite,
		fetchCustomerSites,
		// fetchLPNTransactions,

    // Shared state
		selectedSiteID,
		selectedMaxRecords,
		customerSites,
		deviceMonitorData,
		availableCommands,
		commandResults,
		commandVariables,
		user, // Reactive user object

    // loading and error states
    isLoading,
    // isResetting,
    error,
    clearError,
		lastAPIError,

    // Initialization helper
    initializeUser
  }

}