<template>
  <div class="content-block dx-card responsive-paddings">
		<div class="row">
			<div>
				<span>Site</span>
				<DxSelectBox :dataSource='customerSites.data' v-model='selectedSiteID' value-expr='idcustomers_sites' display-expr='site_name' styling-mode='contained' />
			</div>
			<div>
				<DxButton :width="100" text="Refresh" type="default" styling-mode="contained" @click="onRefreshClick" />
			</div>
		</div>
    <div class="dashboard-container">
			<div class="dashboard-grid" :style="gridStyle">
				<template v-for="widget in sortedWidgets" :key="widget.id">
					<component
							:is="getComponentType(widget.type)"
							:widget-data="widget"
							:data="getDataObject(widget.dataSource)"
							:class="widget.type !== 'statBoxGroup' ? ['dashboard-item', `dashboard-item-${widget.id}`] : ''"
							:style="getItemStyle(widget)"
					/>
				</template>
			</div>
		</div>
  </div>
</template>

<script setup>

	/*=====================================================================
    IMPORTS
 	=====================================================================*/
	// Vue core
  import { ref, computed, onMounted, watch, onUnmounted } from 'vue';

	// UI components & utilities
	import notify from 'devextreme/ui/notify';
	import { DxNumberBox } from 'devextreme-vue/number-box';
	import { DxSelectBox } from 'devextreme-vue';
 import { DxButton } from 'devextreme-vue/button';

	// Composables
	import {useDynamicDash} from '@/composables/useDynamicDash' 
	import { useRemoteSQL } from '@/composables/useRemoteSQL'; 

	// Child components
	import ChartWidget from '@/components/dashboard/chart-widget.vue';
	import GridWidget from 	'@/components/dashboard/grid-widget.vue';
	import StatBoxWidget from	'@/components/dashboard/stat-box-widget.vue';
	import StatBoxGroup from '@/components/dashboard/stat-box-group.vue';
	/*=====================================================================
    COMPOSABLE DEFINITIONS (EXPOSED STATES AND FUNCTIONS)  
  =====================================================================*/
	const { fetchDashboardConfig, getDemoDashboardConfig, getDemoDashboardData } = useDynamicDash();
		const {
		fetchCustomerSites,
		selectedSiteID,
		selectedMaxRecords,
		customerSites,
	
	} = useRemoteSQL();

	/*=====================================================================
    LIFECYCLE HOOKS 
  =====================================================================*/
	onMounted( async() => {
		if (!customerSites.value || !customerSites.value.data || customerSites.value.data.length === 0) {
			console.log('No customer sites data found, fetching customer sites');
			await fetchCustomerSites();
		}
console.log('customerSites:', customerSites.value);
		widgets.value = await getDemoDashboardConfig();
		
		dashboardData.value = await getDemoDashboardData();

		// Set up responsive columns
		handleResize();
		window.addEventListener('resize', handleResize);
	});

	onUnmounted(() => {
		window.removeEventListener('resize', handleResize);
	});


	/*=====================================================================
    ELEMENT REFS
  =====================================================================*/


	/*=====================================================================
    REFS FOR LOCAL STATE
  =====================================================================*/
	const widgets = ref([]);
	const dashboardData = ref([]);
	const loading = ref(true);
	const error = ref(null);
	const columns = ref(12);
	const gridGap = ref(16); 
	const maxRowHeight = ref(300);

	const notifyOptions = ref(
		{ 
			position: { my: "bottom", at: "bottom", of: ".content-block", offset: { x: 0, y: 10 }}, width: 'auto',
			animation: { show: { type: "fade", duration: 800, from: 0, to: 1 }, hide: { type: "fade", duration: 800, from: 1, to: 0 } }
		}
	)


	/*=====================================================================
    COMPUTED PROPERTIES 
  =====================================================================*/ 
	const sortedWidgets = computed(() => {
		return [...widgets.value].sort((a, b) => a.priority - b.priority);
	});

	const gridStyle = computed(() => {
		return {
			gridTemplateColumns: `repeat(${columns.value}, 1fr)`,
			gridAutoRows: `minmax(100px, ${maxRowHeight.value}px)`,
			gap: `${gridGap.value}px` // Use the reactive gap
		};
	});


	/*=====================================================================
		FUNCTIONS
	=====================================================================*/
	const getComponentType = (type) => {
		const componentMap = {
			'chart': ChartWidget,
			'grid': GridWidget,
			'statBox': StatBoxWidget,
			'statBoxGroup': StatBoxGroup
		};
		
		return componentMap[type] || 'div';
	};

	const getDataObject = (key) => {
		if (dashboardData.value && dashboardData.value.data) {
			return dashboardData.value.data[key];
		}
		return [];
	};


	const getItemStyle = (widget) => {
		let colSpan;
		const widgetMinWidth = widget.minWidth || 1;
		const maxColumns = 12; // Define your default max columns, e.g., from your widest layout

		if (columns.value < maxColumns) {
			// For smaller layouts (e.g., 8 or 4 columns),
			// if a widget was designed to be reasonably large (e.g., minWidth >= 4 in a 12-column setup),
			// let it take the full width of the current reduced column layout.
			// This helps fill space effectively on smaller screens.
			// The threshold '4' can be adjusted based on your needs.
			// It implies widgets that would take up 1/3rd or more of the maxColumns layout.
			if (widgetMinWidth >= 4) { 
				colSpan = columns.value;
			} else {
				// For very small widgets, let them keep their original minWidth,
				// but ensure they don't exceed the current available columns.
				colSpan = Math.min(widgetMinWidth, columns.value);
			}
		} else {
			// For the widest layout (e.g., 12 columns),
			// use the widget's specified minWidth, capped by columns.value.
			colSpan = Math.min(widgetMinWidth, columns.value);
		}

		// Ensure colSpan is at least 1 and not more than current columns
		colSpan = Math.max(1, Math.min(colSpan, columns.value));

		const rowSpan = widget.rowSpan || 1;
		
		return {
			gridColumn: `span ${colSpan}`,
			gridRow: `span ${rowSpan}`
		};
	};

	const refreshDashboard = () => {
		getDemoDashboardConfig();
	};

	const handleResize = () => {
		const width = window.innerWidth;
		if (width < 768) {
			columns.value = 4;
			gridGap.value = 8; // Smaller gap for small screens
		} else if (width < 1200) {
			columns.value = 8;
			gridGap.value = 12; // Medium gap
		} else {
			columns.value = 12;
			gridGap.value = 16; // Default gap
		}
	};

	/*=====================================================================
    WATCHERS 
  =====================================================================*/


</script>

<style scoped lang="scss">
	@import "../themes/generated/variables.additional.scss";

	.content-block {
		display: flex;
		flex-direction: column;
		flex: 1 1 auto;
		min-height: 0;
		overflow: hidden;
		position: relative;
		transition: all 0.3s ease;
		color: rgba($base-text-color, 0.65);
		gap: 40px;
	}

  .row {
    display: flex;
    gap: 10px;
    flex-shrink: 0; 
  }

	.dashboard-page {
		width: 100%;
		height: 100%;
		padding: 20px;
		background-color: #f5f7fa;
	}

	.dashboard-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 24px;
	}

	.dashboard-header h1 {
		margin: 0;
		color: #333;
		font-size: 24px;
	}

	.dashboard-actions button {
		padding: 8px 16px;
		background-color: #4a6cf7;
		color: white;
		border: none;
		border-radius: 4px;
		cursor: pointer;
	}

	.dashboard-actions button:disabled {
		background-color: #a0aec0;
		cursor: not-allowed;
	}

	.dashboard-error {
		padding: 16px;
		background-color: #fee2e2;
		border: 1px solid #f87171;
		border-radius: 4px;
		color: #b91c1c;
		margin-bottom: 20px;
	}

	.dashboard-loading {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 200px;
		font-size: 18px;
		color: #6b7280;
	}

	.dashboard-grid {
		display: grid;
		gap: 16px; 
		width: 100%;
		grid-auto-flow: dense; 
	}

	@media (max-width: 1199px) {
		.dashboard-grid {
			gap: 12px;
		}
	}

	@media (max-width: 767px) {
		.dashboard-grid {
			gap: 8px;
		}
	}

	.dashboard-item {
		background-color: rgba($base-bg-dark, 0.3);
		border: 1px solid $base-border-color;
		color: rgba($base-text-color, .65)!important;
		border-radius: 8px;
		padding: 20px;
		overflow: hidden;
	}


	// DX Text Editor Overrides
	::v-deep(.dx-texteditor) {
		border: 1px solid $base-border-color;
		border-radius: 5px;
		background-color: rgba($base-bg-dark, .65);
		padding: 0 10px;
		
	}
	
	::v-deep(.dx-texteditor-input) {
		color: rgba($base-text-color, 0.75)!important;
	}		
	::v-deep(.dx-texteditor::before) {
		content: none;
	}
	::v-deep(.dx-texteditor::after) {
		content: none;
	}

</style>